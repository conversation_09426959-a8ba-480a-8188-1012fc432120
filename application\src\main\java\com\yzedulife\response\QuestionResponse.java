package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 题目响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "题目响应")
public class QuestionResponse {

    @Schema(description = "题目ID")
    private Long id;

    @Schema(description = "所属页面ID")
    private Long pageId;

    @Schema(description = "题干类型", example = "TEXT 或 IMAGE")
    private String contentType;

    @Schema(description = "题干内容")
    private String content;

    @Schema(description = "显示顺序")
    private Integer displayOrder;

    @Schema(description = "正确答案选项代号")
    private Long correctAnswerCode;

    @Schema(description = "选项列表")
    private List<QuestionOptionResponse> options;
}

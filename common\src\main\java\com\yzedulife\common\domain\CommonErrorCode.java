package com.yzedulife.common.domain;


/**
 * 异常编码
 */
public enum CommonErrorCode implements ErrorCode {

	// 产品异常编码 100
	E_100001(100001,"产品不存在"),
	E_100002(100002,"服务类型不存在"),

	// 管理员异常编码 200
	E_200001(200001,"管理员不存在"),

	// 会员异常编码 300
	E_300001(300001,"会员不存在"),

	// 交易订单异常编码 400
	E_400001(400001,"交易订单不存在"),
	E_400002(400002,"验签失败"),
	E_400003(400003,"核验码不存在或已过期"),

	// 验证码异常编码 500
	E_500001(500001,"上个验证码未过期"),
	E_500002(500002,"验证码不存在或已过期"),
	E_500003(500003,"验证码错误"),
	E_500004(500004,"验证码发送失败"),

	// 验证码异常编码 600
	E_600001(600001,"配置不存在"),

	// 其它
	E_NETWORK_ERROR(999996, "网络错误"),
	E_NO_PERMISSION(999997, "访问权限不足"),
	E_NO_AUTHORITY(999998,"未登录或登录信息已过期"),
	/**
	 * 未知错误
	 */
	UNKNOWN(999999,"未知错误");


	private int code;
	private String desc;

	public int getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	private CommonErrorCode(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}


	public static CommonErrorCode setErrorCode(int code) {
       for (CommonErrorCode errorCode : CommonErrorCode.values()) {
           if (errorCode.getCode()==code) {
               return errorCode;
           }
       }
	       return null;
	}

	public static void checkAndThrow(Boolean state, CommonErrorCode error) {
		if (state){
			throw new BusinessException(error);
		}
	}
}

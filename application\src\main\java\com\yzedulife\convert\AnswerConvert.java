package com.yzedulife.convert;

import com.yzedulife.response.AnswerDetailResponse;
import com.yzedulife.response.AnswerSheetResponse;
import com.yzedulife.service.dto.AnswerDetailDTO;
import com.yzedulife.service.dto.AnswerSheetDTO;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.vo.AnswerDetailVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 答案转换工具类
 */
public class AnswerConvert {

    /**
     * AnswerDetailVO转DTO
     */
    public static AnswerDetailDTO detailVo2dto(AnswerDetailVO vo, Long chosenOptionId) {
        if (vo == null) {
            return null;
        }
        AnswerDetailDTO dto = new AnswerDetailDTO();
        dto.setId(vo.getId());
        dto.setAnswerSheetId(vo.getAnswerSheetId());
        dto.setQuestionId(vo.getQuestionId());
        dto.setChosenOptionId(chosenOptionId);
        return dto;
    }

    /**
     * AnswerDetailDTO转Response
     */
    public static AnswerDetailResponse detailDto2response(AnswerDetailDTO dto, String optionCode) {
        if (dto == null) {
            return null;
        }
        AnswerDetailResponse response = new AnswerDetailResponse();
        response.setQuestionId(dto.getQuestionId());
        response.setChosenOptionCode(optionCode);
        return response;
    }

    /**
     * AnswerSheetDTO转Response
     */
    public static AnswerSheetResponse sheetDto2response(AnswerSheetDTO dto) {
        if (dto == null) {
            return null;
        }
        AnswerSheetResponse response = new AnswerSheetResponse();
        response.setId(dto.getId());
        response.setQuestionnaireId(dto.getQuestionnaireId());
        response.setSubmitterType(dto.getSubmitterType());
        response.setStudentUserId(dto.getStudentUserId());
        response.setOtherUserId(dto.getOtherUserId());
        response.setSubmitTime(dto.getSubmitTime());
        return response;
    }

    /**
     * AnswerSheetDTO列表转Response列表
     */
    public static List<AnswerSheetResponse> sheetDto2responseList(List<AnswerSheetDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(AnswerConvert::sheetDto2response)
                .collect(Collectors.toList());
    }
}

-- 测试环境初始化数据

-- 插入测试管理员 (密码已MD5加密)
INSERT INTO admin (id, username, password, role, created_time, updated_time) VALUES
(1, 'testadmin', 'e16b2ab8d12314bf4efbd6203906ea6c', 'admin', NOW(), NOW()),
(2, 'admin2', '482c811da5d5b4bc6d497ffa98491e38', 'admin', NOW(), NOW());

-- 插入测试学生用户
INSERT INTO student_users (id, name, student_number, created_time, updated_time) VALUES 
(1, '张三', '2023001', NOW(), NOW()),
(2, '李四', '2023002', NOW(), NOW()),
(3, '王五', '2023003', NOW(), NOW());

-- 插入测试社会人士用户
INSERT INTO other_users (id, phone, created_time, updated_time) VALUES 
(1, '13800138000', NOW(), NOW()),
(2, '13900139000', NOW(), NOW());

-- 插入测试问卷
INSERT INTO questionnaire (id, title, description, creator_id, created_time, updated_time) VALUES 
(1, '测试问卷1', '这是第一个测试问卷', 1, NOW(), NOW()),
(2, '测试问卷2', '这是第二个测试问卷', 1, NOW(), NOW());

-- 插入测试问卷页面
INSERT INTO questionnaire_page (id, questionnaire_id, title, display_order, created_time, updated_time) VALUES 
(1, 1, '第一页', 1, NOW(), NOW()),
(2, 1, '第二页', 2, NOW(), NOW()),
(3, 2, '第一页', 1, NOW(), NOW());

-- 插入测试题目
INSERT INTO question (id, page_id, title, type, required, display_order, created_time, updated_time) VALUES 
(1, 1, '您的性别是？', 'single', true, 1, NOW(), NOW()),
(2, 1, '您的年龄段是？', 'single', true, 2, NOW(), NOW()),
(3, 2, '您对我们的服务满意吗？', 'single', false, 1, NOW(), NOW()),
(4, 3, '您的职业是？', 'single', true, 1, NOW(), NOW());

-- 插入测试题目选项
INSERT INTO question_option (id, question_id, option_code, option_text, display_order, created_time, updated_time) VALUES 
-- 题目1的选项
(1, 1, 'A', '男', 1, NOW(), NOW()),
(2, 1, 'B', '女', 2, NOW(), NOW()),
-- 题目2的选项
(3, 2, 'A', '18-25岁', 1, NOW(), NOW()),
(4, 2, 'B', '26-35岁', 2, NOW(), NOW()),
(5, 2, 'C', '36-45岁', 3, NOW(), NOW()),
(6, 2, 'D', '46岁以上', 4, NOW(), NOW()),
-- 题目3的选项
(7, 3, 'A', '非常满意', 1, NOW(), NOW()),
(8, 3, 'B', '满意', 2, NOW(), NOW()),
(9, 3, 'C', '一般', 3, NOW(), NOW()),
(10, 3, 'D', '不满意', 4, NOW(), NOW()),
-- 题目4的选项
(11, 4, 'A', '学生', 1, NOW(), NOW()),
(12, 4, 'B', '教师', 2, NOW(), NOW()),
(13, 4, 'C', '工程师', 3, NOW(), NOW()),
(14, 4, 'D', '其他', 4, NOW(), NOW());

-- 插入测试答卷
INSERT INTO answer_sheet (id, questionnaire_id, user_type, user_id, submitted_time) VALUES 
(1, 1, 'student', 1, NOW()),
(2, 1, 'student', 2, NOW()),
(3, 2, 'other', 1, NOW());

-- 插入测试答案详情
INSERT INTO answer_detail (id, answer_sheet_id, question_id, answer_text) VALUES 
-- 答卷1的答案
(1, 1, 1, 'A'),
(2, 1, 2, 'A'),
(3, 1, 3, 'B'),
-- 答卷2的答案
(4, 2, 1, 'B'),
(5, 2, 2, 'B'),
(6, 2, 3, 'A'),
-- 答卷3的答案
(7, 3, 4, 'C');

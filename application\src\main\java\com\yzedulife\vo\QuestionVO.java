package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 题目请求VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "题目请求VO")
public class QuestionVO {
    
    @Schema(description = "题目ID（更新时需要）")
    private Long id;
    
    @Schema(description = "所属页面ID", required = true)
    @NotNull(message = "页面ID不能为空")
    private Long pageId;

    @Schema(description = "题干类型", example = "TEXT 或 IMAGE", required = true)
    @NotBlank(message = "题干类型不能为空")
    private String contentType;
    
    @Schema(description = "题干内容", required = true)
    @NotBlank(message = "题干内容不能为空")
    private String content;
    
    @Schema(description = "显示顺序")
    private Integer displayOrder;
    
    @Schema(description = "选项列表", required = true)
    @NotEmpty(message = "选项列表不能为空")
    @Valid
    private List<QuestionOptionVO> options;
}

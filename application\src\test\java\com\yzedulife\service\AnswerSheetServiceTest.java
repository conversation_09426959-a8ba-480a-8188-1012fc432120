package com.yzedulife.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AnswerSheetDTO;
import com.yzedulife.service.entity.AnswerSheet;
import com.yzedulife.service.mapper.AnswerSheetMapper;
import com.yzedulife.service.mapper.AnswerDetailMapper;
import com.yzedulife.service.service.impl.AnswerSheetServiceImpl;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("答卷服务测试")
class AnswerSheetServiceTest {

    @Mock
    private AnswerSheetMapper answerSheetMapper;

    @Mock
    private AnswerDetailMapper answerDetailMapper;

    @InjectMocks
    private AnswerSheetServiceImpl answerSheetService;

    private AnswerSheet testAnswerSheet;
    private AnswerSheetDTO testAnswerSheetDTO;
    private List<AnswerSheet> testAnswerSheetList;

    @BeforeEach
    void setUp() {
        testAnswerSheetDTO = TestDataFactory.createAnswerSheetDTO();
        testAnswerSheet = new AnswerSheet();
        testAnswerSheet.setId(testAnswerSheetDTO.getId());
        testAnswerSheet.setQuestionnaireId(testAnswerSheetDTO.getQuestionnaireId());
        testAnswerSheet.setSubmitterType(testAnswerSheetDTO.getSubmitterType());
        testAnswerSheet.setStudentUserId(testAnswerSheetDTO.getStudentUserId());
        testAnswerSheet.setOtherUserId(testAnswerSheetDTO.getOtherUserId());
        testAnswerSheet.setSubmitTime(testAnswerSheetDTO.getSubmitTime());

        testAnswerSheetList = new ArrayList<>();
        testAnswerSheetList.add(testAnswerSheet);
        
        AnswerSheet answerSheet2 = new AnswerSheet();
        answerSheet2.setId(2L);
        answerSheet2.setQuestionnaireId(1L);
        answerSheet2.setSubmitterType("SOCIAL");
        answerSheet2.setOtherUserId(1L);
        answerSheet2.setSubmitTime(LocalDateTime.now());
        testAnswerSheetList.add(answerSheet2);
    }

    @Test
    @DisplayName("根据ID获取答卷 - 成功")
    void getById_Success() throws BusinessException {
        // Given
        when(answerSheetMapper.selectById(1L)).thenReturn(testAnswerSheet);

        // When
        AnswerSheetDTO result = answerSheetService.getById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testAnswerSheetDTO.getId(), result.getId());
        assertEquals(testAnswerSheetDTO.getQuestionnaireId(), result.getQuestionnaireId());
        assertEquals(testAnswerSheetDTO.getSubmitterType(), result.getSubmitterType());
        assertEquals(testAnswerSheetDTO.getStudentUserId(), result.getStudentUserId());
        assertEquals(testAnswerSheetDTO.getOtherUserId(), result.getOtherUserId());

        verify(answerSheetMapper).selectById(1L);
    }

    @Test
    @DisplayName("根据ID获取答卷 - ID为空")
    void getById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.getById(null));
        
        assertEquals("答卷ID不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).selectById(any());
    }

    @Test
    @DisplayName("根据ID获取答卷 - 答卷不存在")
    void getById_AnswerSheetNotFound() {
        // Given
        when(answerSheetMapper.selectById(999L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.getById(999L));
        
        assertEquals("答卷不存在", exception.getMessage());
        verify(answerSheetMapper).selectById(999L);
    }

    @Test
    @DisplayName("创建答卷 - 成功")
    void create_Success() throws BusinessException {
        // Given
        when(answerSheetMapper.insert(any(AnswerSheet.class))).thenReturn(1);

        // When
        AnswerSheetDTO result = answerSheetService.create(testAnswerSheetDTO);

        // Then
        assertNotNull(result);
        assertEquals(testAnswerSheetDTO.getQuestionnaireId(), result.getQuestionnaireId());
        assertEquals(testAnswerSheetDTO.getSubmitterType(), result.getSubmitterType());
        assertEquals(testAnswerSheetDTO.getStudentUserId(), result.getStudentUserId());

        verify(answerSheetMapper).insert(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("创建答卷 - 答卷信息为空")
    void create_NullAnswerSheet() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.create(null));
        
        assertEquals("答卷信息不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).insert(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("创建答卷 - 问卷ID为空")
    void create_NullQuestionnaireId() {
        // Given
        testAnswerSheetDTO.setQuestionnaireId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.create(testAnswerSheetDTO));
        
        assertEquals("问卷ID不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).insert(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("创建答卷 - 提交者类型为空")
    void create_EmptySubmitterType() {
        // Given
        testAnswerSheetDTO.setSubmitterType("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.create(testAnswerSheetDTO));
        
        assertEquals("提交者类型不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).insert(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("创建答卷 - 学生用户ID和社会人士用户ID都为空")
    void create_BothUserIdsNull() {
        // Given
        testAnswerSheetDTO.setStudentUserId(null);
        testAnswerSheetDTO.setOtherUserId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.create(testAnswerSheetDTO));
        
        assertEquals("用户ID不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).insert(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("创建答卷 - 数据库插入失败")
    void create_InsertFailed() {
        // Given
        when(answerSheetMapper.insert(any(AnswerSheet.class))).thenReturn(0);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.create(testAnswerSheetDTO));
        
        assertEquals("创建答卷失败", exception.getMessage());
        verify(answerSheetMapper).insert(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("更新答卷 - 成功")
    void update_Success() throws BusinessException {
        // Given
        when(answerSheetMapper.selectById(1L)).thenReturn(testAnswerSheet);
        when(answerSheetMapper.updateById(any(AnswerSheet.class))).thenReturn(1);

        // When
        AnswerSheetDTO result = answerSheetService.update(testAnswerSheetDTO);

        // Then
        assertNotNull(result);
        assertEquals(testAnswerSheetDTO.getId(), result.getId());

        verify(answerSheetMapper).selectById(1L);
        verify(answerSheetMapper).updateById(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("更新答卷 - 答卷信息为空")
    void update_NullAnswerSheet() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.update(null));
        
        assertEquals("答卷信息不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).selectById(any());
        verify(answerSheetMapper, never()).updateById(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("更新答卷 - ID为空")
    void update_NullId() {
        // Given
        testAnswerSheetDTO.setId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.update(testAnswerSheetDTO));
        
        assertEquals("答卷ID不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).selectById(any());
        verify(answerSheetMapper, never()).updateById(any(AnswerSheet.class));
    }

    @Test
    @DisplayName("删除答卷 - 成功")
    void deleteById_Success() throws BusinessException {
        // Given
        when(answerSheetMapper.selectById(1L)).thenReturn(testAnswerSheet);
        when(answerDetailMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(2);
        when(answerSheetMapper.deleteById(1L)).thenReturn(1);

        // When
        Boolean result = answerSheetService.deleteById(1L);

        // Then
        assertTrue(result);
        verify(answerSheetMapper).selectById(1L);
        verify(answerDetailMapper).delete(any(LambdaQueryWrapper.class));
        verify(answerSheetMapper).deleteById(1L);
    }

    @Test
    @DisplayName("删除答卷 - ID为空")
    void deleteById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.deleteById(null));
        
        assertEquals("答卷ID不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).selectById(any());
        verify(answerSheetMapper, never()).deleteById(any());
    }

    @Test
    @DisplayName("根据问卷ID获取答卷列表 - 成功")
    void getByQuestionnaireId_Success() throws BusinessException {
        // Given
        when(answerSheetMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(testAnswerSheetList);

        // When
        List<AnswerSheetDTO> result = answerSheetService.getByQuestionnaireId(1L);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("STUDENT", result.get(0).getSubmitterType());
        assertEquals("SOCIAL", result.get(1).getSubmitterType());

        verify(answerSheetMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据问卷ID获取答卷列表 - 问卷ID为空")
    void getByQuestionnaireId_NullQuestionnaireId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> answerSheetService.getByQuestionnaireId(null));
        
        assertEquals("问卷ID不能为空", exception.getMessage());
        verify(answerSheetMapper, never()).selectList(any());
    }




}

package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.StudentUserConvert;
import com.yzedulife.service.dto.StudentUserDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 学生用户实体类
 */
@Data
@TableName("student_users")
public class StudentUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 学号 (唯一)
     */
    @TableField("student_number")
    private String studentNumber;

    public StudentUserDTO toDTO() {
        return StudentUserConvert.INSTANCE.entity2dto(this);
    }
}

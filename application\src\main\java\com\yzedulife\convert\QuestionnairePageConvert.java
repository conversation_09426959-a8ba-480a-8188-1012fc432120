package com.yzedulife.convert;

import com.yzedulife.response.QuestionnairePageResponse;
import com.yzedulife.service.dto.QuestionnairePageDTO;
import com.yzedulife.vo.QuestionnairePageVO;

/**
 * 问卷页转换工具类
 */
public class QuestionnairePageConvert {

    /**
     * VO转DTO
     */
    public static QuestionnairePageDTO vo2dto(QuestionnairePageVO vo) {
        if (vo == null) {
            return null;
        }
        QuestionnairePageDTO dto = new QuestionnairePageDTO();
        dto.setId(vo.getId());
        dto.setQuestionnaireId(vo.getQuestionnaireId());
        dto.setPageNumber(vo.getPageNumber());
        return dto;
    }

    /**
     * DTO转Response
     */
    public static QuestionnairePageResponse dto2response(QuestionnairePageDTO dto) {
        if (dto == null) {
            return null;
        }
        QuestionnairePageResponse response = new QuestionnairePageResponse();
        response.setId(dto.getId());
        response.setPageNumber(dto.getPageNumber());
        return response;
    }
}

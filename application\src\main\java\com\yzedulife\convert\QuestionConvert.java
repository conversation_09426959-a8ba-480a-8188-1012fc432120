package com.yzedulife.convert;

import com.yzedulife.response.QuestionOptionResponse;
import com.yzedulife.response.QuestionResponse;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.vo.QuestionOptionVO;
import com.yzedulife.vo.QuestionVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 题目转换工具类
 */
public class QuestionConvert {

    /**
     * VO转DTO
     */
    public static QuestionDTO vo2dto(QuestionVO vo) {
        if (vo == null) {
            return null;
        }
        QuestionDTO dto = new QuestionDTO();
        dto.setId(vo.getId());
        dto.setPageId(vo.getPageId());
        dto.setContentType(vo.getContentType());
        dto.setContent(vo.getContent());
        dto.setDisplayOrder(vo.getDisplayOrder());
        return dto;
    }

    /**
     * DTO转Response
     */
    public static QuestionResponse dto2response(QuestionDTO dto) {
        if (dto == null) {
            return null;
        }
        QuestionResponse response = new QuestionResponse();
        response.setId(dto.getId());
        response.setPageId(dto.getPageId());
        response.setContentType(dto.getContentType());
        response.setContent(dto.getContent());
        response.setDisplayOrder(dto.getDisplayOrder());
        response.setCorrectAnswerCode(dto.getCorrectAnswerOptionId());
        return response;
    }

    /**
     * QuestionOptionVO转DTO
     */
    public static QuestionOptionDTO optionVo2dto(QuestionOptionVO vo) {
        if (vo == null) {
            return null;
        }
        QuestionOptionDTO dto = new QuestionOptionDTO();
        dto.setId(vo.getId());
        dto.setOptionType(vo.getOptionType());
        dto.setContent(vo.getContent());
        dto.setOptionCode(vo.getOptionCode());
        return dto;
    }

    /**
     * QuestionOptionDTO转Response
     */
    public static QuestionOptionResponse optionDto2response(QuestionOptionDTO dto) {
        if (dto == null) {
            return null;
        }
        QuestionOptionResponse response = new QuestionOptionResponse();
        response.setId(dto.getId());
        response.setOptionType(dto.getOptionType());
        response.setContent(dto.getContent());
        response.setOptionCode(dto.getOptionCode());
        return response;
    }

    /**
     * QuestionOptionVO列表转DTO列表
     */
    public static List<QuestionOptionDTO> optionVo2dtoList(List<QuestionOptionVO> voList) {
        if (voList == null) {
            return null;
        }
        return voList.stream()
                .map(QuestionConvert::optionVo2dto)
                .collect(Collectors.toList());
    }

    /**
     * QuestionOptionDTO列表转Response列表
     */
    public static List<QuestionOptionResponse> optionDto2responseList(List<QuestionOptionDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(QuestionConvert::optionDto2response)
                .collect(Collectors.toList());
    }
}

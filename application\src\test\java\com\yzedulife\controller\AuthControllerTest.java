package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.service.SmsService;
import com.yzedulife.service.dto.AdminDTO;
import com.yzedulife.service.dto.CodeDTO;
import com.yzedulife.service.dto.OtherUserDTO;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.service.AdminService;
import com.yzedulife.service.service.CodeService;
import com.yzedulife.service.service.OtherUserService;
import com.yzedulife.service.service.StudentUserService;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuthController.class)
@ActiveProfiles("test")
@DisplayName("认证控制器测试")
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AdminService adminService;

    @MockBean
    private StudentUserService studentUserService;

    @MockBean
    private OtherUserService otherUserService;

    @MockBean
    private CodeService codeService;

    @MockBean
    private SmsService smsService;

    private AdminDTO testAdmin;
    private StudentUserDTO testStudent;
    private OtherUserDTO testOtherUser;
    private CodeDTO testCode;

    @BeforeEach
    void setUp() {
        testAdmin = TestDataFactory.createAdminDTO();
        testStudent = TestDataFactory.createStudentUserDTO();
        testOtherUser = TestDataFactory.createOtherUserDTO();
        testCode = TestDataFactory.createCodeDTO();
    }

    @Test
    @DisplayName("管理员登录 - 成功")
    void loginAdmin_Success() throws Exception {
        // Given
        when(adminService.validateLogin("testadmin", "testpassword"))
                .thenReturn(testAdmin);

        // When & Then
        mockMvc.perform(post("/auth/loginAdmin")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("username", "testadmin")
                        .param("password", "testpassword"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists());

        verify(adminService).validateLogin("testadmin", "testpassword");
    }

    @Test
    @DisplayName("管理员登录 - 用户名或密码错误")
    void loginAdmin_InvalidCredentials() throws Exception {
        // Given
        when(adminService.validateLogin("wronguser", "wrongpass"))
                .thenThrow(new BusinessException("用户名或密码错误"));

        // When & Then
        mockMvc.perform(post("/auth/loginAdmin")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("username", "wronguser")
                        .param("password", "wrongpass"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("用户名或密码错误"));

        verify(adminService).validateLogin("wronguser", "wrongpass");
    }

    @Test
    @DisplayName("学生登录 - 成功")
    void loginStudent_Success() throws Exception {
        // Given
        when(studentUserService.getByStudentNumber("2023001"))
                .thenReturn(testStudent);

        // When & Then
        mockMvc.perform(post("/auth/loginStudent")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("name", "张三")
                        .param("studentNumber", "2023001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists());

        verify(studentUserService).getByStudentNumber("2023001");
    }

    @Test
    @DisplayName("学生登录 - 学生信息不匹配")
    void loginStudent_InfoMismatch() throws Exception {
        // Given
        when(studentUserService.getByStudentNumber("2023001"))
                .thenReturn(testStudent);

        // When & Then
        mockMvc.perform(post("/auth/loginStudent")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("name", "李四") // 错误的姓名
                        .param("studentNumber", "2023001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("学生信息不匹配"));

        verify(studentUserService).getByStudentNumber("2023001");
    }

    @Test
    @DisplayName("学生登录 - 学生不存在")
    void loginStudent_StudentNotFound() throws Exception {
        // Given
        when(studentUserService.getByStudentNumber("9999999"))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(post("/auth/loginStudent")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("name", "不存在的学生")
                        .param("studentNumber", "9999999"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("学生信息不匹配"));

        verify(studentUserService).getByStudentNumber("9999999");
    }

    @Test
    @DisplayName("其他人员登录 - 成功（已存在用户）")
    void loginOther_Success_ExistingUser() throws Exception {
        // Given
        when(codeService.query("***********")).thenReturn(testCode);
        when(otherUserService.getByPhone("***********")).thenReturn(testOtherUser);

        // When & Then
        mockMvc.perform(post("/auth/loginOther")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "***********")
                        .param("code", "123456"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists());

        verify(codeService).query("***********");
        verify(otherUserService).getByPhone("***********");
        verify(codeService).delete("***********");
    }

    @Test
    @DisplayName("其他人员登录 - 成功（新用户）")
    void loginOther_Success_NewUser() throws Exception {
        // Given
        when(codeService.query("***********")).thenReturn(testCode);
        when(otherUserService.getByPhone("***********"))
                .thenThrow(new BusinessException("用户不存在"));
        
        OtherUserDTO newUser = TestDataFactory.createOtherUserDTO(2L, "***********");
        when(otherUserService.create(any(OtherUserDTO.class))).thenReturn(newUser);

        // When & Then
        mockMvc.perform(post("/auth/loginOther")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "***********")
                        .param("code", "123456"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists());

        verify(codeService).query("***********");
        verify(otherUserService).getByPhone("***********");
        verify(otherUserService).create(any(OtherUserDTO.class));
        verify(codeService).delete("***********");
    }

    @Test
    @DisplayName("其他人员登录 - 验证码过期")
    void loginOther_CodeExpired() throws Exception {
        // Given
        CodeDTO expiredCode = TestDataFactory.createExpiredCodeDTO();
        when(codeService.query("***********")).thenReturn(expiredCode);

        // When & Then
        mockMvc.perform(post("/auth/loginOther")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "***********")
                        .param("code", "123456"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));

        verify(codeService).query("***********");
        verify(otherUserService, never()).getByPhone(anyString());
    }

    @Test
    @DisplayName("其他人员登录 - 验证码错误")
    void loginOther_WrongCode() throws Exception {
        // Given
        when(codeService.query("***********")).thenReturn(testCode);

        // When & Then
        mockMvc.perform(post("/auth/loginOther")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "***********")
                        .param("code", "654321")) // 错误的验证码
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));

        verify(codeService).query("***********");
        verify(otherUserService, never()).getByPhone(anyString());
    }

    @Test
    @DisplayName("发送验证码 - 成功")
    void sendCode_Success() throws Exception {
        // Given
        when(codeService.create(eq("***********"), anyString(), eq(120L)))
                .thenReturn(testCode);
        doNothing().when(smsService).sendAliCode(eq("***********"), anyString());

        // When & Then
        mockMvc.perform(post("/auth/sendCode")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "***********"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("验证码发送成功"));

        verify(codeService).create(eq("***********"), anyString(), eq(120L));
        verify(smsService).sendAliCode(eq("***********"), anyString());
    }

    @Test
    @DisplayName("发送验证码 - 短信服务异常")
    void sendCode_SmsServiceError() throws Exception {
        // Given
        when(codeService.create(eq("***********"), anyString(), eq(120L)))
                .thenReturn(testCode);
        doThrow(new BusinessException("短信发送失败"))
                .when(smsService).sendAliCode(eq("***********"), anyString());

        // When & Then
        mockMvc.perform(post("/auth/sendCode")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "***********"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("短信发送失败"));

        verify(codeService).create(eq("***********"), anyString(), eq(120L));
        verify(smsService).sendAliCode(eq("***********"), anyString());
    }
}

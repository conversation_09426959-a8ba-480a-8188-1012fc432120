package com.yzedulife.convert;

import com.yzedulife.response.QuestionnaireResponse;
import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.vo.QuestionnaireVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 问卷转换工具类
 */
public class QuestionnaireConvert {

    /**
     * VO转DTO
     */
    public static QuestionnaireDTO vo2dto(QuestionnaireVO vo) {
        if (vo == null) {
            return null;
        }
        QuestionnaireDTO dto = new QuestionnaireDTO();
        dto.setId(vo.getId());
        dto.setTitle(vo.getTitle());
        dto.setTargetAudience(vo.getTargetAudience());
        dto.setStatus(vo.getStatus());
        return dto;
    }

    /**
     * DTO转Response
     */
    public static QuestionnaireResponse dto2response(QuestionnaireDTO dto) {
        if (dto == null) {
            return null;
        }
        QuestionnaireResponse response = new QuestionnaireResponse();
        response.setId(dto.getId());
        response.setTitle(dto.getTitle());
        response.setTargetAudience(dto.getTargetAudience());
        response.setStatus(dto.getStatus());
        response.setCreatorId(dto.getCreatorId());
        return response;
    }

    /**
     * DTO列表转Response列表
     */
    public static List<QuestionnaireResponse> dto2responseList(List<QuestionnaireDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(QuestionnaireConvert::dto2response)
                .collect(Collectors.toList());
    }
}

package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.convert.QuestionConvert;
import com.yzedulife.response.QuestionResponse;
import com.yzedulife.response.Response;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.service.service.QuestionOptionService;
import com.yzedulife.service.service.QuestionService;
import com.yzedulife.vo.QuestionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/question")
@Tag(name = "题目模块")
public class QuestionController {

    @Autowired
    private QuestionService questionService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Token("admin")
    @Operation(summary = "创建题目")
    @PostMapping("/create")
    @Transactional
    public Response create(@Valid @RequestBody QuestionVO questionVO) {
        try {
            // 转换题目信息
            QuestionDTO questionDTO = QuestionConvert.vo2dto(questionVO);
            
            // 如果没有指定显示顺序，自动设置为最后一个
            if (questionDTO.getDisplayOrder() == null) {
                Integer count = questionService.getCountByPageId(questionDTO.getPageId());
                questionDTO.setDisplayOrder(count + 1);
            }
            
            // 创建题目
            QuestionDTO createdQuestion = questionService.create(questionDTO);
            
            // 创建选项
            List<QuestionOptionDTO> optionDTOs = QuestionConvert.optionVo2dtoList(questionVO.getOptions());
            for (QuestionOptionDTO optionDTO : optionDTOs) {
                optionDTO.setQuestionId(createdQuestion.getId());
                // 如果没有指定选项代号，自动生成
                if (optionDTO.getOptionCode() == null || optionDTO.getOptionCode().isEmpty()) {
                    optionDTO.setOptionCode(questionOptionService.generateOptionCode(createdQuestion.getId()));
                }
                questionOptionService.create(optionDTO);
            }
            
            // 获取完整的题目信息（包含选项）
            List<QuestionOptionDTO> createdOptions = questionOptionService.getByQuestionId(createdQuestion.getId());
            QuestionResponse response = QuestionConvert.dto2response(createdQuestion);
            response.setOptions(QuestionConvert.optionDto2responseList(createdOptions));
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建题目失败", e);
            return Response.error().msg("创建题目失败");
        }
    }

    @Token("admin")
    @Operation(summary = "修改题目")
    @PostMapping("/update")
    @Transactional
    public Response update(@Valid @RequestBody QuestionVO questionVO) {
        try {
            if (questionVO.getId() == null) {
                return Response.error().msg("题目ID不能为空");
            }
            
            // 更新题目信息
            QuestionDTO questionDTO = QuestionConvert.vo2dto(questionVO);
            QuestionDTO updatedQuestion = questionService.update(questionDTO);
            
            // 删除原有选项
            questionOptionService.deleteByQuestionId(questionVO.getId());
            
            // 创建新选项
            List<QuestionOptionDTO> optionDTOs = QuestionConvert.optionVo2dtoList(questionVO.getOptions());
            for (QuestionOptionDTO optionDTO : optionDTOs) {
                optionDTO.setQuestionId(questionVO.getId());
                optionDTO.setId(null); // 确保是新增
                // 如果没有指定选项代号，自动生成
                if (optionDTO.getOptionCode() == null || optionDTO.getOptionCode().isEmpty()) {
                    optionDTO.setOptionCode(questionOptionService.generateOptionCode(questionVO.getId()));
                }
                questionOptionService.create(optionDTO);
            }
            
            // 获取完整的题目信息（包含选项）
            List<QuestionOptionDTO> createdOptions = questionOptionService.getByQuestionId(questionVO.getId());
            QuestionResponse response = QuestionConvert.dto2response(updatedQuestion);
            response.setOptions(QuestionConvert.optionDto2responseList(createdOptions));
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("修改题目失败", e);
            return Response.error().msg("修改题目失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除题目")
    @PostMapping("/delete")
    @Transactional
    public Response delete(@RequestParam Long id) {
        try {
            // 删除题目的所有选项
            questionOptionService.deleteByQuestionId(id);
            
            // 删除题目
            Boolean result = questionService.deleteById(id);
            if (result) {
                return Response.success().msg("删除成功");
            } else {
                return Response.error().msg("删除失败");
            }
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除题目失败", e);
            return Response.error().msg("删除题目失败");
        }
    }
}

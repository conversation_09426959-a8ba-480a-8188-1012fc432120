package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.convert.QuestionConvert;
import com.yzedulife.convert.QuestionnaireConvert;
import com.yzedulife.convert.QuestionnairePageConvert;
import com.yzedulife.response.*;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import com.yzedulife.util.SecurityUtil;
import com.yzedulife.vo.QuestionnaireVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/questionnaire")
@Tag(name = "问卷模块")
public class QuestionnaireController {

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private QuestionnairePageService questionnairePageService;

    @Autowired
    private QuestionService questionService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Token("admin")
    @Operation(summary = "创建问卷")
    @PostMapping("/create")
    public Response create(@Valid @RequestBody QuestionnaireVO questionnaireVO) {
        try {
            // 获取当前管理员ID
            String token = SecurityUtil.getToken();
            String adminId = JwtUtil.getId(token);
            
            QuestionnaireDTO dto = QuestionnaireConvert.vo2dto(questionnaireVO);
            dto.setCreatorId(Long.parseLong(adminId));
            
            QuestionnaireDTO result = questionnaireService.create(dto);
            QuestionnaireResponse response = QuestionnaireConvert.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建问卷失败", e);
            return Response.error().msg("创建问卷失败");
        }
    }

    @Token("admin")
    @Operation(summary = "修改问卷")
    @PostMapping("/update")
    public Response update(@Valid @RequestBody QuestionnaireVO questionnaireVO) {
        try {
            if (questionnaireVO.getId() == null) {
                return Response.error().msg("问卷ID不能为空");
            }
            
            QuestionnaireDTO dto = QuestionnaireConvert.vo2dto(questionnaireVO);
            QuestionnaireDTO result = questionnaireService.update(dto);
            QuestionnaireResponse response = QuestionnaireConvert.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("修改问卷失败", e);
            return Response.error().msg("修改问卷失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除问卷")
    @PostMapping("/delete")
    public Response delete(@RequestParam Long id) {
        try {
            Boolean result = questionnaireService.deleteById(id);
            if (result) {
                return Response.success().msg("删除成功");
            } else {
                return Response.error().msg("删除失败");
            }
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除问卷失败", e);
            return Response.error().msg("删除问卷失败");
        }
    }

    @Token("admin")
    @Operation(summary = "列出所有问卷")
    @GetMapping("/list")
    public Response list() {
        try {
            List<QuestionnaireDTO> dtoList = questionnaireService.getAll();
            List<QuestionnaireResponse> responseList = QuestionnaireConvert.dto2responseList(dtoList);
            
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取问卷列表失败", e);
            return Response.error().msg("获取问卷列表失败");
        }
    }

    @Token("student other")
    @Operation(summary = "问卷详情")
    @GetMapping("/detail")
    public Response detail(@RequestParam Long id) {
        try {
            // 获取问卷基本信息
            QuestionnaireDTO questionnaireDTO = questionnaireService.getById(id);
            QuestionnaireResponse response = QuestionnaireConvert.dto2response(questionnaireDTO);

            // 获取问卷的所有页面
            List<QuestionnairePageDTO> pages = questionnairePageService.getByQuestionnaireId(id);
            List<QuestionnairePageResponse> pageResponses = new ArrayList<>();

            for (QuestionnairePageDTO page : pages) {
                QuestionnairePageResponse pageResponse = QuestionnairePageConvert.dto2response(page);

                // 获取页面的所有题目
                List<QuestionDTO> questions = questionService.getByPageId(page.getId());
                List<QuestionResponse> questionResponses = new ArrayList<>();

                for (QuestionDTO question : questions) {
                    QuestionResponse questionResponse = QuestionConvert.dto2response(question);

                    // 获取题目的所有选项
                    List<QuestionOptionDTO> options = questionOptionService.getByQuestionId(question.getId());
                    List<QuestionOptionResponse> optionResponses = QuestionConvert.optionDto2responseList(options);
                    questionResponse.setOptions(optionResponses);

                    questionResponses.add(questionResponse);
                }

                pageResponse.setQuestions(questionResponses);
                pageResponses.add(pageResponse);
            }

            response.setPages(pageResponses);
            return Response.success().data(response);

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取问卷详情失败", e);
            return Response.error().msg("获取问卷详情失败");
        }
    }
}

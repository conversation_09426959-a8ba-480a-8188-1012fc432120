package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 答案详情请求VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "答案详情请求VO")
public class AnswerDetailVO {
    
    @Schema(description = "答案详情ID（更新时需要）")
    private Long id;
    
    @Schema(description = "答卷ID", required = true)
    @NotNull(message = "答卷ID不能为空")
    private Long answerSheetId;
    
    @Schema(description = "题目ID", required = true)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;
    
    @Schema(description = "所选选项代号", required = true)
    @NotNull(message = "所选选项代号不能为空")
    private String chosenOptionCode;
}

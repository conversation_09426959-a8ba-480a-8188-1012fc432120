-- 测试环境数据库初始化脚本
SET NAMES utf8mb4;

-- ===========================
-- 用户管理表 (User Management)
-- ===========================

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名 (唯一)',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `role` VARCHAR(20) NOT NULL COMMENT '角色',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 学生用户表
CREATE TABLE IF NOT EXISTS `student_users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '学生ID',
    `name` VARCHAR(50) NOT NULL COMMENT '姓名',
    `student_number` VARCHAR(20) NOT NULL UNIQUE COMMENT '学号 (唯一)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 社会人士用户表
CREATE TABLE IF NOT EXISTS `other_users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '社会人士ID',
    `phone` VARCHAR(11) NOT NULL UNIQUE COMMENT '手机号 (唯一)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- ===========================
-- 问卷与内容表 (Questionnaire & Content)
-- ===========================

-- 问卷表
CREATE TABLE IF NOT EXISTS `questionnaire` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '问卷ID',
    `title` VARCHAR(200) NOT NULL COMMENT '问卷标题',
    `description` TEXT COMMENT '问卷描述',
    `creator_id` BIGINT NOT NULL COMMENT '创建者ID',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 问卷页面表
CREATE TABLE IF NOT EXISTS `questionnaire_page` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '页面ID',
    `questionnaire_id` BIGINT NOT NULL COMMENT '问卷ID',
    `title` VARCHAR(200) NOT NULL COMMENT '页面标题',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 题目表
CREATE TABLE IF NOT EXISTS `question` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '题目ID',
    `page_id` BIGINT NOT NULL COMMENT '页面ID',
    `title` VARCHAR(500) NOT NULL COMMENT '题目标题',
    `type` VARCHAR(20) NOT NULL COMMENT '题目类型',
    `required` BOOLEAN DEFAULT FALSE COMMENT '是否必答',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 题目选项表
CREATE TABLE IF NOT EXISTS `question_option` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '选项ID',
    `question_id` BIGINT NOT NULL COMMENT '题目ID',
    `option_code` VARCHAR(10) NOT NULL COMMENT '选项代号',
    `option_text` VARCHAR(500) NOT NULL COMMENT '选项文本',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- ===========================
-- 答案表 (Answer)
-- ===========================

-- 答卷表
CREATE TABLE IF NOT EXISTS `answer_sheet` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '答卷ID',
    `questionnaire_id` BIGINT NOT NULL COMMENT '问卷ID',
    `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `submitted_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    PRIMARY KEY (`id`)
);

-- 答案详情表
CREATE TABLE IF NOT EXISTS `answer_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '答案ID',
    `answer_sheet_id` BIGINT NOT NULL COMMENT '答卷ID',
    `question_id` BIGINT NOT NULL COMMENT '题目ID',
    `answer_text` TEXT COMMENT '答案文本',
    PRIMARY KEY (`id`)
);
